name: xtuner-env
channels:
  - pytorch
  - nvidia
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - brotli-python=1.0.9=py310h6a678d5_8
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2024.11.26=h06a4308_0
  - certifi=2024.8.30=py310h06a4308_0
  - cuda-cudart=12.1.105=0
  - cuda-cupti=12.1.105=0
  - cuda-libraries=12.1.0=0
  - cuda-nvrtc=12.1.105=0
  - cuda-nvtx=12.1.105=0
  - cuda-opencl=12.6.77=0
  - cuda-runtime=12.1.0=0
  - cuda-version=12.6=3
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.12.1=h4a9f257_0
  - gmp=6.2.1=h295c915_3
  - gmpy2=2.1.2=py310heeb90bb_0
  - gnutls=3.6.15=he1e5248_0
  - intel-openmp=2023.1.0=hdb19cb5_46306
  - jinja2=3.1.4=py310h06a4308_1
  - jpeg=9e=h5eee18b_3
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.40=h12ee557_0
  - lerc=3.0=h295c915_0
  - libcublas=*********=0
  - libcufft=********=0
  - libcufile=********=0
  - libcurand=*********=0
  - libcusolver=*********=0
  - libcusparse=*********=0
  - libdeflate=1.17=h5eee18b_1
  - libffi=3.4.4=h6a678d5_1
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h5eee18b_3
  - libidn2=2.3.4=h5eee18b_0
  - libjpeg-turbo=2.0.0=h9bf148f_0
  - libnpp=12.0.2.50=0
  - libnvjitlink=12.1.105=0
  - libnvjpeg=12.1.1.14=0
  - libpng=1.6.39=h5eee18b_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.19.0=h5eee18b_0
  - libtiff=4.5.1=h6a678d5_0
  - libunistring=0.9.10=h27cfd23_0
  - libuuid=1.41.5=h5eee18b_0
  - libwebp-base=1.3.2=h5eee18b_1
  - llvm-openmp=14.0.6=h9e868ea_0
  - lz4-c=1.9.4=h6a678d5_1
  - mkl=2023.1.0=h213fc3f_46344
  - mkl-service=2.4.0=py310h5eee18b_1
  - mkl_fft=1.3.11=py310h5eee18b_0
  - mkl_random=1.2.8=py310h1128e8f_0
  - mpc=1.1.0=h10f8cd9_1
  - mpfr=4.0.2=hb69a4c5_1
  - mpmath=1.3.0=py310h06a4308_0
  - ncurses=6.4=h6a678d5_0
  - nettle=3.7.3=hbbd107a_1
  - numpy-base=2.0.1=py310hb5e798b_1
  - openh264=2.1.1=h4ff587b_0
  - openjpeg=2.5.2=he7f1fd0_0
  - openssl=3.0.15=h5eee18b_0
  - pillow=11.0.0=py310hfdbf927_0
  - pip=24.2=py310h06a4308_0
  - pysocks=1.7.1=py310h06a4308_0
  - python=3.10.15=he870216_1
  - pytorch=2.4.0=py3.10_cuda12.1_cudnn9.1.0_0
  - pytorch-cuda=12.1=ha16c6d3_6
  - pytorch-mutex=1.0=cuda
  - pyyaml=6.0.2=py310h5eee18b_0
  - readline=8.2=h5eee18b_0
  - requests=2.32.3=py310h06a4308_1
  - setuptools=75.1.0=py310h06a4308_0
  - sqlite=3.45.3=h5eee18b_0
  - tbb=2021.8.0=hdb19cb5_0
  - tk=8.6.14=h39e8969_0
  - torchtriton=3.0.0=py310
  - torchvision=0.19.0=py310_cu121
  - typing_extensions=4.11.0=py310h06a4308_0
  - urllib3=2.2.3=py310h06a4308_0
  - wheel=0.44.0=py310h06a4308_0
  - xz=5.4.6=h5eee18b_1
  - yaml=0.2.5=h7b6447c_0
  - zlib=1.2.13=h5eee18b_1
  - zstd=1.5.6=hc292b87_0
  - pip:
      - accelerate==1.2.0
      - addict==2.4.0
      - aiohappyeyeballs==2.4.4
      - aiohttp==3.11.10
      - aiosignal==1.3.1
      - altair==5.5.0
      - annotated-types==0.7.0
      - anyio==4.7.0
      - argon2-cffi==23.1.0
      - argon2-cffi-bindings==21.2.0
      - arrow==1.3.0
      - arxiv==2.1.3
      - asttokens==3.0.0
      - async-lru==2.0.4
      - async-timeout==5.0.1
      - attrs==24.2.0
      - babel==2.16.0
      - beautifulsoup4==4.12.3
      - bitsandbytes==0.43.3
      - bleach==6.2.0
      - blinker==1.9.0
      - brotli==1.1.0
      - cachetools==5.5.0
      - cffi==1.17.1
      - charset-normalizer==3.4.0
      - click==8.1.7
      - colorama==0.4.6
      - comm==0.2.2
      - contourpy==1.3.1
      - cycler==0.12.1
      - datasets==3.1.0
      - debugpy==1.8.9
      - decorator==5.1.1
      - deepspeed==0.16.1
      - defusedxml==0.7.1
      - dill==0.3.8
      - distro==1.9.0
      - duckduckgo-search==5.3.1b1
      - einops==0.8.0
      - et-xmlfile==2.0.0
      - exceptiongroup==1.2.2
      - executing==2.1.0
      - fairscale==0.4.13
      - fastjsonschema==2.21.1
      - feedparser==6.0.11
      - filelock==3.16.1
      - flash-attn==2.5.8
      - fonttools==4.55.2
      - fqdn==1.5.1
      - frozenlist==1.5.0
      - fsspec==2024.9.0
      - func-timeout==4.3.5
      - gitdb==4.0.11
      - gitpython==3.1.43
      - griffe==0.49.0
      - h11==0.14.0
      - h2==4.1.0
      - hjson==3.1.0
      - hpack==4.0.0
      - httpcore==1.0.7
      - httpx==0.28.1
      - huggingface-hub==0.26.5
      - hyperframe==6.0.1
      - idna==3.10
      - imageio==2.36.1
      - ipykernel==6.29.5
      - ipython==8.30.0
      - ipywidgets==8.1.5
      - isoduration==20.11.0
      - jedi==0.19.2
      - jiter==0.8.0
      - json5==0.10.0
      - jsonpointer==3.0.0
      - jsonschema==4.23.0
      - jsonschema-specifications==2024.10.1
      - jupyter==1.1.1
      - jupyter-client==8.6.3
      - jupyter-console==6.6.3
      - jupyter-core==5.7.2
      - jupyter-events==0.10.0
      - jupyter-lsp==2.2.5
      - jupyter-server==2.14.2
      - jupyter-server-terminals==0.5.3
      - jupyterlab==4.3.2
      - jupyterlab-pygments==0.3.0
      - jupyterlab-server==2.27.3
      - jupyterlab-widgets==3.0.13
      - kiwisolver==1.4.7
      - lagent==0.2.4
      - lazy-loader==0.4
      - markdown-it-py==3.0.0
      - markupsafe==3.0.2
      - matplotlib==3.9.3
      - matplotlib-inline==0.1.7
      - mdurl==0.1.2
      - mistune==3.0.2
      - mmengine==0.10.5
      - modelscope==1.21.0
      - mpi4py-mpich==3.1.5
      - msgpack==1.1.0
      - multidict==6.1.0
      - multiprocess==0.70.16
      - narwhals==1.16.0
      - nbclient==0.10.1
      - nbconvert==7.16.4
      - nbformat==5.10.4
      - nest-asyncio==1.6.0
      - networkx==3.4.2
      - ninja==********
      - notebook==7.3.1
      - notebook-shim==0.2.4
      - numpy==1.26.4
      - nvidia-cublas-cu12==********
      - nvidia-cuda-cupti-cu12==12.1.105
      - nvidia-cuda-nvrtc-cu12==12.1.105
      - nvidia-cuda-runtime-cu12==12.1.105
      - nvidia-cudnn-cu12==********
      - nvidia-cufft-cu12==*********
      - nvidia-curand-cu12==**********
      - nvidia-cusolver-cu12==**********
      - nvidia-cusparse-cu12==**********
      - nvidia-nccl-cu12==2.20.5
      - nvidia-nvjitlink-cu12==12.4.127
      - nvidia-nvtx-cu12==12.1.105
      - openai==1.57.0
      - opencv-python==*********
      - opencv-python-headless==*********
      - openpyxl==3.1.5
      - overrides==7.7.0
      - packaging==24.2
      - pandas==2.2.3
      - pandocfilters==1.5.1
      - parso==0.8.4
      - peft==0.12.0
      - pexpect==4.9.0
      - phx-class-registry==4.1.0
      - platformdirs==4.3.6
      - prometheus-client==0.21.1
      - prompt-toolkit==3.0.48
      - propcache==0.2.1
      - protobuf==5.29.1
      - psutil==6.1.0
      - ptflops==0.7.4
      - ptyprocess==0.7.0
      - pure-eval==0.2.3
      - py-cpuinfo==9.0.0
      - pyarrow==18.1.0
      - pycparser==2.22
      - pydantic==2.10.3
      - pydantic-core==2.27.1
      - pydeck==0.9.1
      - pygments==2.18.0
      - pyparsing==3.2.0
      - python-dateutil==2.9.0.post0
      - python-json-logger==2.0.7
      - pytz==2024.2
      - pyzmq==26.2.0
      - referencing==0.35.1
      - regex==2024.11.6
      - rfc3339-validator==0.1.4
      - rfc3986-validator==0.1.1
      - rich==13.9.4
      - rpds-py==0.22.3
      - safetensors==0.4.5
      - scikit-image==0.24.0
      - scipy==1.14.1
      - seaborn==0.13.2
      - send2trash==1.8.3
      - sentencepiece==0.2.0
      - sgmllib3k==1.0.0
      - six==1.17.0
      - smmap==5.0.1
      - sniffio==1.3.1
      - socksio==1.0.0
      - soupsieve==2.6
      - stack-data==0.6.3
      - streamlit==1.40.2
      - sympy==1.13.1
      - tenacity==9.0.0
      - termcolor==2.5.0
      - terminado==0.18.1
      - thop==0.1.1-2209072238
      - tifffile==2024.9.20
      - tiktoken==0.8.0
      - timeout-decorator==0.5.0
      - timm==1.0.12
      - tinycss2==1.4.0
      - tokenizers==0.19.1
      - toml==0.10.2
      - tomli==2.2.1
      - torchaudio==2.4.1
      - torchstat==0.0.7
      - tornado==6.4.2
      - tqdm==4.67.1
      - traitlets==5.14.3
      - transformers==4.40.0
      - transformers-stream-generator==0.0.5
      - triton==3.0.0
      - types-python-dateutil==2.9.0.20241206
      - typing-extensions==4.12.2
      - tzdata==2024.2
      - uri-template==1.3.0
      - watchdog==6.0.0
      - wcwidth==0.2.13
      - webcolors==24.11.1
      - webencodings==0.5.1
      - websocket-client==1.8.0
      - widgetsnbextension==4.0.13
      - xxhash==3.5.0
      - yapf==0.43.0
      - yarl==1.18.3
      - zstandard==0.23.0
prefix: software/anaconda3/envs/xtuner-env
