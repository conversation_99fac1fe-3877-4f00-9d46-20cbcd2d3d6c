{"gradient_accumulation_steps": "auto", "train_micro_batch_size_per_gpu": "auto", "gradient_clipping": "auto", "zero_allow_untested_optimizer": true, "zero_force_ds_cpu_optimizer": false, "zero_optimization": {"stage": 3, "overlap_comm": true, "offload_optimizer": {"device": "cpu", "pin_memory": true}, "offload_param": {"device": "cpu", "pin_memory": true}, "stage3_gather_16bit_weights_on_model_save": true}, "fp16": {"enabled": "auto", "initial_scale_power": 16}, "bf16": {"enabled": "auto"}}