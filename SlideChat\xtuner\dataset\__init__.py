# Copyright (c) OpenMMLab. All rights reserved.
import warnings

from .concat_dataset import ConcatDataset
from .huggingface import process_hf_dataset
from .intern_repo import (build_packed_dataset,
                          load_intern_repo_tokenized_dataset,
                          load_intern_repo_untokenized_dataset)
from .json_dataset import load_json_file
from .llava import LLaVADataset
from .modelscope import process_ms_dataset
from .moss_sft import MOSSSFTDataset
from .refcoco_json import (InvRefCOCOJsonDataset, RefCOCOJsonDataset,
                           RefCOCOJsonEvalDataset)
from .utils import decode_base64_to_image, expand2square, load_image

# ignore FutureWarning in hf datasets
warnings.simplefilter(action='ignore', category=FutureWarning)



# cying
# __all__ = [
#     'process_hf_dataset', 'ConcatDataset', 'MOSSSFTDataset',
#     'process_ms_dataset', 'LLaVADataset', 'expand2square',
#     'decode_base64_to_image', 'load_image', 'process_ms_dataset',
#     'load_intern_repo_tokenized_dataset',
#     'load_intern_repo_untokenized_dataset', 'build_packed_dataset',
#     'RefCOCOJsonDataset', 'RefCOCOJsonEvalDataset', 'InvRefCOCOJsonDataset',
#     'load_json_file'
# ]
__all__ = [
    'process_hf_dataset', 'ConcatDataset', 'MOSSSFTDataset',
    'process_ms_dataset', 'LLaVADataset', 'expand2square',
    'decode_base64_to_image', 'load_image', 'process_ms_dataset',
    'load_intern_repo_tokenized_dataset',
    'load_intern_repo_untokenized_dataset', 'build_packed_dataset',
    'RefCOCOJsonDataset', 'RefCOCOJsonEvalDataset', 'InvRefCOCOJsonDataset'
]
# cying