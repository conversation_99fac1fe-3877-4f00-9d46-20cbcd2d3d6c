"""
SlideChat编码器推理工具
提供便于集成到其他项目的推理接口
"""

import torch
import torch.nn as nn
import pandas as pd
import numpy as np
from typing import Union, List, Dict, Optional, Tuple
import os
import warnings

from slide_chat_encoder import SlideChatEncoder
from slide_chat_config import SlideChatEncoderConfig


class SlideChatInference:
    """SlideChat编码器推理类"""
    
    def __init__(self, 
                 model_path: str, 
                 device: str = 'cuda',
                 dtype: torch.dtype = torch.float16):
        """
        初始化推理器
        
        Args:
            model_path: 模型保存路径
            device: 推理设备
            dtype: 数据类型
        """
        self.device = device
        self.dtype = dtype
        
        # 加载模型
        self.encoder = SlideChatEncoder.from_pretrained(model_path, dtype)
        self.encoder.to(device)
        self.encoder.eval()
        
        print(f"✓ 模型已加载到 {device}")
        print(f"✓ 模型配置: {self.encoder.config.longnet_config_name}")
    
    def encode_wsi_features(self, 
                           wsi_features: Union[str, np.ndarray, torch.Tensor],
                           max_patches: Optional[int] = None) -> torch.Tensor:
        """
        编码WSI特征
        
        Args:
            wsi_features: WSI特征，可以是:
                - CSV文件路径
                - numpy数组 [num_patches, feature_dim]
                - torch张量 [num_patches, feature_dim]
            max_patches: 最大patch数量限制
        
        Returns:
            encoded_features: 编码后的特征 [1, num_patches, llm_hidden_size]
        """
        # 处理输入
        if isinstance(wsi_features, str):
            # 从CSV文件加载
            features = self._load_features_from_csv(wsi_features)
        elif isinstance(wsi_features, np.ndarray):
            features = torch.from_numpy(wsi_features.astype(np.float32))
        elif isinstance(wsi_features, torch.Tensor):
            features = wsi_features.float()
        else:
            raise ValueError(f"不支持的输入类型: {type(wsi_features)}")
        
        # 限制patch数量
        if max_patches is not None and features.shape[0] > max_patches:
            # 随机采样
            indices = torch.randperm(features.shape[0])[:max_patches]
            features = features[indices]
        
        # 添加batch维度
        if features.dim() == 2:
            features = features.unsqueeze(0)  # [1, num_patches, feature_dim]
        
        # 移到设备并转换类型
        features = features.to(self.device, dtype=self.dtype)
        
        # 推理
        with torch.no_grad():
            encoded_features = self.encoder(features)
        
        return encoded_features
    
    def _load_features_from_csv(self, csv_path: str) -> torch.Tensor:
        """从CSV文件加载特征"""
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f"特征文件不存在: {csv_path}")
        
        try:
            df = pd.read_csv(csv_path)
            # 去掉最后一列的patch名称
            features = df.iloc[:, :-1].values.astype(np.float32)
            return torch.from_numpy(features)
        except Exception as e:
            raise RuntimeError(f"加载CSV文件失败 {csv_path}: {e}")
    
    def encode_batch(self, 
                     wsi_list: List[Union[str, np.ndarray, torch.Tensor]],
                     max_patches: Optional[int] = None) -> List[torch.Tensor]:
        """
        批量编码多个WSI
        
        Args:
            wsi_list: WSI特征列表
            max_patches: 最大patch数量限制
        
        Returns:
            encoded_list: 编码后的特征列表
        """
        encoded_list = []
        
        for wsi_features in wsi_list:
            try:
                encoded = self.encode_wsi_features(wsi_features, max_patches)
                encoded_list.append(encoded)
            except Exception as e:
                warnings.warn(f"编码WSI失败: {e}")
                # 返回零特征作为fallback
                zero_features = torch.zeros(
                    1, max_patches or 1000, self.encoder.config.llm_hidden_size,
                    device=self.device, dtype=self.dtype
                )
                encoded_list.append(zero_features)
        
        return encoded_list
    
    def get_feature_statistics(self, encoded_features: torch.Tensor) -> Dict[str, float]:
        """获取编码特征的统计信息"""
        with torch.no_grad():
            stats = {
                'mean': encoded_features.mean().item(),
                'std': encoded_features.std().item(),
                'min': encoded_features.min().item(),
                'max': encoded_features.max().item(),
                'shape': list(encoded_features.shape)
            }
        return stats


class SlideChatFeatureExtractor(nn.Module):
    """
    可集成到其他模型中的特征提取器
    继承自nn.Module，便于作为其他模型的组件
    """
    
    def __init__(self, encoder_path: str, freeze: bool = True):
        """
        Args:
            encoder_path: 编码器模型路径
            freeze: 是否冻结参数
        """
        super().__init__()
        
        self.encoder = SlideChatEncoder.from_pretrained(encoder_path)
        
        if freeze:
            for param in self.encoder.parameters():
                param.requires_grad = False
            self.encoder.eval()
    
    def forward(self, wsi_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            wsi_features: [batch_size, num_patches, input_dim]
        
        Returns:
            encoded_features: [batch_size, num_patches, llm_hidden_size]
        """
        return self.encoder(wsi_features)


def create_inference_pipeline(model_path: str, 
                            device: str = 'cuda',
                            dtype: torch.dtype = torch.float16) -> SlideChatInference:
    """便捷函数：创建推理管道"""
    return SlideChatInference(model_path, device, dtype)


def batch_process_wsi_directory(model_path: str,
                               wsi_dir: str,
                               output_dir: str,
                               max_patches: int = 1000,
                               batch_size: int = 4) -> None:
    """
    批量处理WSI目录中的所有特征文件
    
    Args:
        model_path: 模型路径
        wsi_dir: WSI特征文件目录
        output_dir: 输出目录
        max_patches: 最大patch数量
        batch_size: 批处理大小
    """
    # 创建推理器
    inference = create_inference_pipeline(model_path)
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有CSV文件
    csv_files = [f for f in os.listdir(wsi_dir) if f.endswith('.csv')]
    
    print(f"找到 {len(csv_files)} 个WSI特征文件")
    
    # 批量处理
    for i in range(0, len(csv_files), batch_size):
        batch_files = csv_files[i:i+batch_size]
        batch_paths = [os.path.join(wsi_dir, f) for f in batch_files]
        
        print(f"处理批次 {i//batch_size + 1}: {len(batch_files)} 个文件")
        
        # 编码特征
        encoded_list = inference.encode_batch(batch_paths, max_patches)
        
        # 保存结果
        for j, (filename, encoded_features) in enumerate(zip(batch_files, encoded_list)):
            output_name = filename.replace('.csv', '_encoded.pt')
            output_path = os.path.join(output_dir, output_name)
            
            # 保存为PyTorch张量
            torch.save(encoded_features.cpu(), output_path)
            
            print(f"  ✓ 已保存: {output_name}")
    
    print("✓ 批量处理完成！")


# 示例使用代码
if __name__ == "__main__":
    # 示例1: 单个WSI推理
    model_path = "./checkpoints/best_model"
    wsi_csv = "./SlideChat/dataset/WSI_feat/TCGA-A7-A0CJ-01Z-00-DX2.csv"
    
    if os.path.exists(model_path) and os.path.exists(wsi_csv):
        # 创建推理器
        inference = create_inference_pipeline(model_path)
        
        # 编码WSI特征
        encoded_features = inference.encode_wsi_features(wsi_csv, max_patches=500)
        
        print(f"编码后特征形状: {encoded_features.shape}")
        print(f"特征统计: {inference.get_feature_statistics(encoded_features)}")
    
    # 示例2: 批量处理
    # batch_process_wsi_directory(
    #     model_path="./checkpoints/best_model",
    #     wsi_dir="./SlideChat/dataset/WSI_feat",
    #     output_dir="./encoded_features",
    #     max_patches=1000,
    #     batch_size=2
    # )
