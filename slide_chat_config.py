"""
SlideChat编码器配置文件
用于配置LongNet编码器和Projector投影层的参数
"""

from dataclasses import dataclass
from typing import List, Optional


@dataclass
class SlideChatEncoderConfig:
    """SlideChat编码器配置类"""
    
    # LongNet配置
    longnet_layers: int = 2
    longnet_dim: int = 512
    longnet_attention_heads: int = 8
    longnet_dropout: float = 0.1
    longnet_drop_path_rate: float = 0.1
    dilated_ratio: List[int] = None
    segment_length: List[int] = None
    
    # Projector配置
    projector_depth: int = 2
    projector_hidden_act: str = "gelu"
    projector_bias: bool = True
    
    # 输入输出维度
    input_dim: int = 512  # patch特征维度
    llm_hidden_size: int = 4096  # LLM隐藏层维度
    
    # 训练配置
    freeze_longnet: bool = False
    use_gradient_checkpointing: bool = True
    
    def __post_init__(self):
        """初始化后处理"""
        if self.dilated_ratio is None:
            self.dilated_ratio = [1, 2, 4, 8, 16]
        if self.segment_length is None:
            self.segment_length = [1024, 2048, 4096, 8192, 16384]
    
    @property
    def longnet_config_name(self) -> str:
        """生成LongNet配置名称"""
        return f"LongNet_{self.longnet_layers}_layers_{self.longnet_dim}_dim"
    
    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            'longnet_layers': self.longnet_layers,
            'longnet_dim': self.longnet_dim,
            'longnet_attention_heads': self.longnet_attention_heads,
            'longnet_dropout': self.longnet_dropout,
            'longnet_drop_path_rate': self.longnet_drop_path_rate,
            'dilated_ratio': self.dilated_ratio,
            'segment_length': self.segment_length,
            'projector_depth': self.projector_depth,
            'projector_hidden_act': self.projector_hidden_act,
            'projector_bias': self.projector_bias,
            'input_dim': self.input_dim,
            'llm_hidden_size': self.llm_hidden_size,
            'freeze_longnet': self.freeze_longnet,
            'use_gradient_checkpointing': self.use_gradient_checkpointing
        }
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> 'SlideChatEncoderConfig':
        """从字典创建配置"""
        return cls(**config_dict)
    
    def save_to_file(self, file_path: str):
        """保存配置到文件"""
        import json
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load_from_file(cls, file_path: str) -> 'SlideChatEncoderConfig':
        """从文件加载配置"""
        import json
        with open(file_path, 'r', encoding='utf-8') as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


# 预定义配置
DEFAULT_CONFIG = SlideChatEncoderConfig()

SMALL_CONFIG = SlideChatEncoderConfig(
    longnet_layers=1,
    longnet_dim=256,
    projector_depth=1,
    llm_hidden_size=2048
)

LARGE_CONFIG = SlideChatEncoderConfig(
    longnet_layers=4,
    longnet_dim=1024,
    projector_depth=3,
    llm_hidden_size=8192
)
