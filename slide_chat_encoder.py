"""
独立的SlideChat编码器模块
包含LongNet编码器和Projector投影层，用于处理WSI patch特征
"""

import torch
import torch.nn as nn
import os
import sys
from typing import Dict, Optional, Tuple
import warnings

# 添加SlideChat路径到sys.path
SLIDECHAT_PATH = "./SlideChat"
if SLIDECHAT_PATH not in sys.path:
    sys.path.insert(0, SLIDECHAT_PATH)

# 尝试导入SlideChat模块
make_longnet_from_name = None
ProjectorModel = None
ProjectorConfig = None

try:
    from xtuner.model.torchscale.model.LongNet import make_longnet_from_name
    from xtuner.model.modules.projector import ProjectorModel, ProjectorConfig
    print("✓ SlideChat模块导入成功")
except ImportError as e:
    print(f"⚠️ SlideChat模块导入失败: {e}")
    print("请先安装SlideChat依赖:")
    print("1. cd SlideChat")
    print("2. pip install -e .")
    print("3. 或者安装缺失的依赖: pip install mmengine transformers")

    # 创建占位符类以避免运行时错误
    class DummyProjectorConfig:
        def __init__(self, **kwargs):
            for k, v in kwargs.items():
                setattr(self, k, v)

    class DummyProjectorModel(nn.Module):
        def __init__(self, config):
            super().__init__()
            self.config = config
            # 创建一个简单的线性层作为占位符
            self.model = nn.Linear(config.visual_hidden_size, config.llm_hidden_size)

        def forward(self, x):
            return self.model(x)

        def save_pretrained(self, path):
            os.makedirs(path, exist_ok=True)
            torch.save(self.state_dict(), os.path.join(path, "pytorch_model.bin"))

        @classmethod
        def from_pretrained(cls, path):
            # 简化的加载逻辑
            return cls(DummyProjectorConfig(visual_hidden_size=512, llm_hidden_size=4096))

        def enable_input_require_grads(self):
            pass

        def gradient_checkpointing_enable(self):
            pass

    def dummy_make_longnet_from_name(config_name, **kwargs):
        """占位符LongNet创建函数"""
        class DummyLongNet(nn.Module):
            def __init__(self):
                super().__init__()
                self.encoder = nn.TransformerEncoder(
                    nn.TransformerEncoderLayer(d_model=512, nhead=8, batch_first=False),
                    num_layers=2
                )

            def forward(self, src_tokens=None, token_embeddings=None):
                if token_embeddings is not None:
                    output = self.encoder(token_embeddings)
                    return {"encoder_out": output}
                return {"encoder_out": torch.zeros(1, 1, 512)}

            def save_pretrained(self, path):
                os.makedirs(path, exist_ok=True)
                torch.save(self.state_dict(), os.path.join(path, "pytorch_model.bin"))

        return DummyLongNet()

    # 使用占位符
    if make_longnet_from_name is None:
        make_longnet_from_name = dummy_make_longnet_from_name
    if ProjectorModel is None:
        ProjectorModel = DummyProjectorModel
    if ProjectorConfig is None:
        ProjectorConfig = DummyProjectorConfig

from slide_chat_config import SlideChatEncoderConfig


class SlideChatEncoder(nn.Module):
    """
    独立的SlideChat编码器
    
    包含:
    1. LongNet编码器 - 处理长序列的WSI patches
    2. Projector投影层 - 将特征映射到LLM空间
    
    Args:
        config: SlideChatEncoderConfig配置对象
        dtype: 模型数据类型，默认torch.float16
    """
    
    def __init__(self, config: SlideChatEncoderConfig, dtype: torch.dtype = torch.float16):
        super().__init__()
        self.config = config
        self.dtype = dtype
        
        # 初始化LongNet编码器
        self._init_longnet_encoder()
        
        # 初始化Projector投影层
        self._init_projector()
        
        # 设置梯度检查点
        if config.use_gradient_checkpointing:
            self.enable_gradient_checkpointing()
        
        # 冻结参数设置
        if config.freeze_longnet:
            self.freeze_longnet()
    
    def _init_longnet_encoder(self):
        """初始化LongNet编码器"""
        try:
            self.longnet_encoder = make_longnet_from_name(
                config_name=self.config.longnet_config_name,
                dilated_ratio=str(self.config.dilated_ratio),
                segment_length=str(self.config.segment_length),
                drop_path_rate=self.config.longnet_drop_path_rate,
                dropout=self.config.longnet_dropout
            )
            print(f"✓ LongNet编码器初始化成功: {self.config.longnet_config_name}")
        except Exception as e:
            raise RuntimeError(f"LongNet编码器初始化失败: {e}")
    
    def _init_projector(self):
        """初始化Projector投影层"""
        try:
            projector_config = ProjectorConfig(
                visual_hidden_size=self.config.longnet_dim,
                llm_hidden_size=self.config.llm_hidden_size,
                depth=self.config.projector_depth,
                hidden_act=self.config.projector_hidden_act,
                bias=self.config.projector_bias
            )
            self.projector = ProjectorModel(projector_config).to(self.dtype)
            print(f"✓ Projector投影层初始化成功: {self.config.projector_depth}层")
        except Exception as e:
            raise RuntimeError(f"Projector投影层初始化失败: {e}")
    
    def enable_gradient_checkpointing(self):
        """启用梯度检查点以节省显存"""
        if hasattr(self.projector, 'gradient_checkpointing_enable'):
            self.projector.gradient_checkpointing_enable()
        if hasattr(self.projector, 'enable_input_require_grads'):
            self.projector.enable_input_require_grads()
        print("✓ 梯度检查点已启用")
    
    def freeze_longnet(self):
        """冻结LongNet编码器参数"""
        for param in self.longnet_encoder.parameters():
            param.requires_grad = False
        print("✓ LongNet编码器参数已冻结")
    
    def unfreeze_longnet(self):
        """解冻LongNet编码器参数"""
        for param in self.longnet_encoder.parameters():
            param.requires_grad = True
        print("✓ LongNet编码器参数已解冻")
    
    def forward(self, patch_features: torch.Tensor) -> torch.Tensor:
        """
        前向传播
        
        Args:
            patch_features: WSI patch特征 [batch_size, num_patches, input_dim]
        
        Returns:
            encoded_features: 编码后的特征 [batch_size, num_patches, llm_hidden_size]
        """
        batch_size, num_patches, input_dim = patch_features.shape
        
        # 确保输入维度正确
        if input_dim != self.config.input_dim:
            raise ValueError(f"输入特征维度 {input_dim} 与配置不匹配 {self.config.input_dim}")
        
        # 转换数据类型
        patch_features = patch_features.to(self.dtype)
        
        # LongNet编码 - 需要调整维度顺序 [num_patches, batch_size, input_dim]
        longnet_input = patch_features.permute(1, 0, 2)
        
        # LongNet前向传播
        longnet_output = self.longnet_encoder(
            src_tokens=None, 
            token_embeddings=longnet_input
        )["encoder_out"]  # [num_patches, batch_size, longnet_dim]
        
        # 调整回原始维度顺序 [batch_size, num_patches, longnet_dim]
        longnet_features = longnet_output.permute(1, 0, 2)
        
        # Projector投影
        encoded_features = self.projector(longnet_features)
        
        return encoded_features
    
    def get_trainable_parameters(self) -> Dict[str, int]:
        """获取可训练参数统计"""
        longnet_params = sum(p.numel() for p in self.longnet_encoder.parameters() if p.requires_grad)
        projector_params = sum(p.numel() for p in self.projector.parameters() if p.requires_grad)
        total_params = longnet_params + projector_params
        
        return {
            'longnet_trainable': longnet_params,
            'projector_trainable': projector_params,
            'total_trainable': total_params
        }
    
    def print_model_info(self):
        """打印模型信息"""
        params_info = self.get_trainable_parameters()
        print("\n" + "="*50)
        print("SlideChat编码器模型信息")
        print("="*50)
        print(f"LongNet层数: {self.config.longnet_layers}")
        print(f"LongNet维度: {self.config.longnet_dim}")
        print(f"Projector深度: {self.config.projector_depth}")
        print(f"输入维度: {self.config.input_dim}")
        print(f"输出维度: {self.config.llm_hidden_size}")
        print("-"*50)
        print(f"LongNet可训练参数: {params_info['longnet_trainable']:,}")
        print(f"Projector可训练参数: {params_info['projector_trainable']:,}")
        print(f"总可训练参数: {params_info['total_trainable']:,}")
        print("="*50)

    def save_pretrained(self, save_dir: str):
        """保存模型权重和配置"""
        os.makedirs(save_dir, exist_ok=True)

        # 保存配置
        config_path = os.path.join(save_dir, "config.json")
        self.config.save_to_file(config_path)

        # 保存LongNet编码器
        longnet_dir = os.path.join(save_dir, "longnet_encoder")
        os.makedirs(longnet_dir, exist_ok=True)
        if hasattr(self.longnet_encoder, 'save_pretrained'):
            self.longnet_encoder.save_pretrained(longnet_dir)
        else:
            torch.save(self.longnet_encoder.state_dict(),
                      os.path.join(longnet_dir, "pytorch_model.bin"))

        # 保存Projector
        projector_dir = os.path.join(save_dir, "projector")
        self.projector.save_pretrained(projector_dir)

        print(f"✓ 模型已保存到: {save_dir}")

    @classmethod
    def from_pretrained(cls, model_dir: str, dtype: torch.dtype = torch.float16):
        """从保存的权重加载模型"""
        # 加载配置
        config_path = os.path.join(model_dir, "config.json")
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")

        config = SlideChatEncoderConfig.load_from_file(config_path)

        # 创建模型实例
        model = cls(config, dtype)

        # 加载LongNet权重
        longnet_dir = os.path.join(model_dir, "longnet_encoder")
        if os.path.exists(os.path.join(longnet_dir, "pytorch_model.bin")):
            longnet_state = torch.load(os.path.join(longnet_dir, "pytorch_model.bin"))
            model.longnet_encoder.load_state_dict(longnet_state)

        # 加载Projector权重
        projector_dir = os.path.join(model_dir, "projector")
        if os.path.exists(projector_dir):
            model.projector = ProjectorModel.from_pretrained(projector_dir).to(dtype)

        print(f"✓ 模型已从 {model_dir} 加载")
        return model

    def load_from_slidechat_checkpoint(self, checkpoint_path: str):
        """从SlideChat原始检查点加载权重"""
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"检查点文件不存在: {checkpoint_path}")

        checkpoint = torch.load(checkpoint_path, map_location='cpu')

        # 提取LongNet权重
        longnet_state_dict = {}
        projector_state_dict = {}

        for key, value in checkpoint.items():
            if key.startswith('LongNet_encoder.'):
                new_key = key.replace('LongNet_encoder.', '')
                longnet_state_dict[new_key] = value
            elif key.startswith('projector.'):
                new_key = key.replace('projector.', '')
                projector_state_dict[new_key] = value

        # 加载权重
        if longnet_state_dict:
            self.longnet_encoder.load_state_dict(longnet_state_dict, strict=False)
            print("✓ LongNet权重已加载")

        if projector_state_dict:
            self.projector.load_state_dict(projector_state_dict, strict=False)
            print("✓ Projector权重已加载")

        print(f"✓ 从SlideChat检查点加载完成: {checkpoint_path}")


def create_encoder_from_config(config_name: str = "default", **kwargs) -> SlideChatEncoder:
    """便捷函数：根据配置名称创建编码器"""
    if config_name == "default":
        config = SlideChatEncoderConfig(**kwargs)
    elif config_name == "small":
        from slide_chat_config import SMALL_CONFIG
        config = SMALL_CONFIG
        # 更新自定义参数
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
    elif config_name == "large":
        from slide_chat_config import LARGE_CONFIG
        config = LARGE_CONFIG
        # 更新自定义参数
        for key, value in kwargs.items():
            if hasattr(config, key):
                setattr(config, key, value)
    else:
        raise ValueError(f"未知的配置名称: {config_name}")

    return SlideChatEncoder(config)
