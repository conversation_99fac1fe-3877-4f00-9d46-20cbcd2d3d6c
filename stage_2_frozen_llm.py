# Copyright (c) OpenMMLab. All rights reserved.
import torch
from mmengine.dataset import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from mmengine.hooks import (<PERSON><PERSON><PERSON><PERSON>, DistSampler<PERSON><PERSON>Hook, Iter<PERSON><PERSON><PERSON><PERSON><PERSON>,
                            <PERSON><PERSON><PERSON><PERSON>, ParamSchedulerHook)
from mmengine.optim import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>e<PERSON>nne<PERSON><PERSON>, <PERSON><PERSON><PERSON>
from torch.optim import <PERSON><PERSON>
from transformers import (AutoModelForCausalLM, AutoTokenizer,
                          BitsAndBytesConfig, CLIPImageProcessor,
                          CLIPVisionModel)
 
from xtuner.dataset import LLaVADataset
from xtuner.dataset.collate_fns import default_collate_fn
from xtuner.dataset.map_fns import llava_map_fn, template_map_fn_factory
from xtuner.engine.hooks import <PERSON>setInfoHook, EvaluateChatHook, HFCheckpointHook
from xtuner.engine.runner import TrainLoop
from xtuner.model import LLaVAModel
from xtuner.utils import PROMPT_TEMPLATE

#######################################################################
#                          PART 1  Settings                           #
#######################################################################
# Model
llm_name_or_path = 'Qwen/Qwen2.5-7B-Instruct'
# 如果有stage1的预训练权重，在这里指定路径
pretrained_pth = None  # 'work_dirs/stage1/iter_xxx.pth'

# Data
data_path = 'SlideInstruct_train_stage2_VQA.json'
image_path_list = None

prompt_template = PROMPT_TEMPLATE.qwen_chat

max_length = 51200
per_image_length = None
sample_type='wsi' # 'wsi'or'image'

# Scheduler & Optimizer - 针对显存优化的设置
batch_size = 1  # 减小batch size以节省显存
accumulative_counts = 16  # 增加梯度累积来补偿小batch size
dataloader_num_workers = 8  # 减少worker数量
max_epochs = 10
optim_type = AdamW
lr = 5e-5  # 稍微降低学习率，因为只训练部分参数
betas = (0.9, 0.999)
weight_decay = 0.01
max_norm = 1  # grad clip
warmup_ratio = 0.03

# Save
save_steps = 200  # 更频繁地保存检查点
save_total_limit = 3  # 保留更多检查点

# Evaluate the generation performance during the training
evaluation_freq = 500
SYSTEM = ''
evaluation_images = './BLCA/TCGA-GV-A40G-01Z-00-DX1.csv'
evaluation_inputs = ['Generate an overview summarizing the principal findings from the pathology examination of the whole slide image.']

#######################################################################
#            PART 2  Model & Tokenizer & Image Processor              #
#######################################################################
tokenizer = dict(
    type=AutoTokenizer.from_pretrained,
    pretrained_model_name_or_path=llm_name_or_path,
    trust_remote_code=True,
    padding_side='right')

# 关键修改：冻结LLM，使用量化来进一步节省显存
model = dict(
    type=LLaVAModel,
    freeze_llm=True,  # 🔥 冻结LLM参数
    pretrained_pth=pretrained_pth,
    train_stage='2',  # 保持stage2的逻辑
    llm=dict(
        type=AutoModelForCausalLM.from_pretrained,
        pretrained_model_name_or_path=llm_name_or_path,
        trust_remote_code=True,
        torch_dtype=torch.float16,
        # 添加量化配置进一步节省显存
        quantization_config=dict(
            type=BitsAndBytesConfig,
            load_in_4bit=True,
            load_in_8bit=False,
            llm_int8_threshold=6.0,
            llm_int8_has_fp16_weight=False,
            bnb_4bit_compute_dtype=torch.float16,
            bnb_4bit_use_double_quant=True,
            bnb_4bit_quant_type='nf4'
        )
    )
)

#######################################################################
#                      PART 3  Dataset & Dataloader                   #
#######################################################################
llava_dataset = dict(
    type=LLaVADataset,
    data_path=data_path,
    image_folder='',
    image_path_list=image_path_list,
    tokenizer=tokenizer,
    dataset_map_fn=llava_map_fn,
    template_map_fn=dict(
        type=template_map_fn_factory, template=prompt_template),
    max_length=max_length,
    per_image_length=per_image_length,
    pad_image_to_square=False)

train_dataloader = dict(
    batch_size=batch_size,
    num_workers=dataloader_num_workers,
    pin_memory=True,
    dataset=llava_dataset,
    sampler=dict(type=DefaultSampler, shuffle=True),
    collate_fn=dict(type=default_collate_fn))

#######################################################################
#                    PART 4  Scheduler & Optimizer                    #
#######################################################################
# optimizer - 只优化可训练的参数
optim_wrapper = dict(
    type=AmpOptimWrapper,
    optimizer=dict(
        type=optim_type, lr=lr, betas=betas, weight_decay=weight_decay),
    clip_grad=dict(max_norm=max_norm, error_if_nonfinite=False),
    accumulative_counts=accumulative_counts,
    loss_scale='dynamic',
    dtype='float16')

# learning policy
param_scheduler = [
    dict(
        type=LinearLR,
        start_factor=1e-5,
        by_epoch=True,
        begin=0,
        end=warmup_ratio * max_epochs,
        convert_to_iter_based=True),
    dict(
        type=CosineAnnealingLR,
        eta_min=0.0,
        by_epoch=True,
        begin=warmup_ratio * max_epochs,
        end=max_epochs,
        convert_to_iter_based=True)
]

# train, val, test setting
train_cfg = dict(type=TrainLoop, max_epochs=max_epochs)

#######################################################################
#                           PART 5  Runtime                           #
#######################################################################
# Log the dialogue periodically during the training process, optional
custom_hooks = [
    dict(type=DatasetInfoHook, tokenizer=tokenizer),
    dict(
        type=EvaluateChatHook,
        tokenizer=tokenizer,
        every_n_iters=evaluation_freq,
        evaluation_inputs=evaluation_inputs,
        evaluation_images=evaluation_images,
        system=SYSTEM,
        prompt_template=prompt_template)
]

# configure default hooks
default_hooks = dict(
    # record the time of every iteration.
    timer=dict(type=IterTimerHook),
    # print log every 10 iterations.
    logger=dict(type=LoggerHook, log_metric_by_epoch=False, interval=10),
    # enable the parameter scheduler.
    param_scheduler=dict(type=ParamSchedulerHook),
    # save checkpoint per `save_steps`.
    checkpoint=dict(
        type=CheckpointHook,
        by_epoch=False,
        interval=save_steps,
        max_keep_ckpts=save_total_limit),
    # set sampler seed in distributed evrionment.
    sampler_seed=dict(type=DistSamplerSeedHook),
)

# configure environment
env_cfg = dict(
    # whether to enable cudnn benchmark
    cudnn_benchmark=False,
    # set multi process parameters
    mp_cfg=dict(mp_start_method='fork', opencv_num_threads=0),
    # set distributed parameters
    dist_cfg=dict(backend='nccl'),
)

# set visualizer
visualizer = None

# set log level
log_level = 'INFO'

# load from which checkpoint
load_from = None

# whether to resume training from the loaded checkpoint
resume = False
 
# Defaults to use random seed and disable `deterministic`
randomness = dict(seed=None, deterministic=False)

# set log processor
log_processor = dict(by_epoch=False)

#######################################################################
#                    显存优化相关说明                                   #
#######################################################################
"""
显存优化策略说明：

1. 冻结LLM参数 (freeze_llm=True)
   - 只训练LongNet编码器和Projector投影层
   - 大幅减少可训练参数数量和显存使用

2. 使用4bit量化 (load_in_4bit=True)
   - 进一步压缩LLM的显存占用
   - 对冻结的参数影响很小

3. 减小batch_size并增加梯度累积
   - batch_size=1, accumulative_counts=16
   - 等效于batch_size=16的训练效果

4. 启用混合精度训练 (dtype='float16')
   - 减少显存使用，加速训练

5. 减少数据加载worker数量
   - 降低CPU内存使用

使用方法：
xtuner train stage_2_frozen_llm.py \
  --deepspeed configs/deepspeed/deepspeed_zero2.json \
  --work-dir work_dirs/stage2_frozen

预期显存使用：
- 原版stage2: ~40GB+
- 冻结版本: ~15-20GB (取决于具体配置)
"""
