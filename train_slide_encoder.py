"""
独立训练SlideChat编码器的脚本
支持冻结LLM，只训练LongNet和Projector
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
import os
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import argparse
from tqdm import tqdm
import warnings

from slide_chat_encoder import SlideChatEncoder, create_encoder_from_config
from slide_chat_config import SlideChatEncoderConfig


class WSIDataset(Dataset):
    """WSI数据集类"""
    
    def __init__(self, data_file: str, feature_dir: str, max_patches: int = 1000):
        """
        Args:
            data_file: JSON格式的对话数据文件
            feature_dir: WSI特征文件目录
            max_patches: 最大patch数量
        """
        self.feature_dir = feature_dir
        self.max_patches = max_patches
        
        # 加载对话数据
        with open(data_file, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        print(f"✓ 加载了 {len(self.data)} 个样本")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        
        # 获取图像特征文件路径
        image_file = item['image'][0]  # 假设只有一个图像
        feature_path = os.path.join(self.feature_dir, image_file)
        
        # 加载WSI特征
        try:
            features_df = pd.read_csv(feature_path)
            # 去掉最后一列的patch名称，只保留特征
            features = features_df.iloc[:, :-1].values.astype(np.float32)
            
            # 限制patch数量
            if len(features) > self.max_patches:
                # 随机采样或取前N个
                indices = np.random.choice(len(features), self.max_patches, replace=False)
                features = features[indices]
            
            # 转换为tensor
            features = torch.from_numpy(features)
            
        except Exception as e:
            warnings.warn(f"加载特征文件失败 {feature_path}: {e}")
            # 返回零特征作为fallback
            features = torch.zeros(self.max_patches, 512)
        
        # 获取对话文本（这里简化处理，实际可能需要更复杂的文本处理）
        conversations = item['conversations']
        question = conversations[0]['value'] if conversations else ""
        answer = conversations[1]['value'] if len(conversations) > 1 else ""
        
        return {
            'features': features,
            'question': question,
            'answer': answer,
            'image_id': item.get('id', f'sample_{idx}')
        }


def collate_fn(batch):
    """数据批处理函数"""
    # 获取最大patch数量
    max_patches = max(item['features'].shape[0] for item in batch)
    batch_size = len(batch)
    feature_dim = batch[0]['features'].shape[1]
    
    # 创建padded tensor
    features = torch.zeros(batch_size, max_patches, feature_dim)
    attention_masks = torch.zeros(batch_size, max_patches, dtype=torch.bool)
    
    questions = []
    answers = []
    image_ids = []
    
    for i, item in enumerate(batch):
        feat = item['features']
        seq_len = feat.shape[0]
        
        features[i, :seq_len] = feat
        attention_masks[i, :seq_len] = True
        
        questions.append(item['question'])
        answers.append(item['answer'])
        image_ids.append(item['image_id'])
    
    return {
        'features': features,
        'attention_masks': attention_masks,
        'questions': questions,
        'answers': answers,
        'image_ids': image_ids
    }


class SlideChatTrainer:
    """SlideChat编码器训练器"""
    
    def __init__(self, 
                 encoder: SlideChatEncoder,
                 train_dataloader: DataLoader,
                 val_dataloader: Optional[DataLoader] = None,
                 learning_rate: float = 1e-4,
                 weight_decay: float = 0.01,
                 device: str = 'cuda'):
        
        self.encoder = encoder.to(device)
        self.train_dataloader = train_dataloader
        self.val_dataloader = val_dataloader
        self.device = device
        
        # 设置优化器（只优化可训练参数）
        trainable_params = [p for p in self.encoder.parameters() if p.requires_grad]
        self.optimizer = optim.AdamW(trainable_params, lr=learning_rate, weight_decay=weight_decay)
        
        # 简单的损失函数（这里用MSE作为示例，实际可能需要更复杂的损失）
        self.criterion = nn.MSELoss()
        
        print(f"✓ 训练器初始化完成，设备: {device}")
        print(f"✓ 可训练参数数量: {sum(p.numel() for p in trainable_params):,}")
    
    def train_epoch(self) -> float:
        """训练一个epoch"""
        self.encoder.train()
        total_loss = 0.0
        num_batches = 0
        
        progress_bar = tqdm(self.train_dataloader, desc="Training")
        
        for batch in progress_bar:
            features = batch['features'].to(self.device)
            attention_masks = batch['attention_masks'].to(self.device)
            
            # 前向传播
            self.optimizer.zero_grad()
            
            # 编码特征
            encoded_features = self.encoder(features)
            
            # 简单的自监督损失（重构损失）
            # 实际应用中可能需要与LLM的损失结合
            target = features  # 简化：用输入作为目标
            if encoded_features.shape[-1] != target.shape[-1]:
                # 如果维度不匹配，创建一个简单的目标
                target = torch.randn_like(encoded_features)
            
            loss = self.criterion(encoded_features, target)
            
            # 反向传播
            loss.backward()
            self.optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            progress_bar.set_postfix({'loss': loss.item()})
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        return avg_loss
    
    def validate(self) -> float:
        """验证"""
        if self.val_dataloader is None:
            return 0.0
        
        self.encoder.eval()
        total_loss = 0.0
        num_batches = 0
        
        with torch.no_grad():
            for batch in tqdm(self.val_dataloader, desc="Validation"):
                features = batch['features'].to(self.device)
                
                encoded_features = self.encoder(features)
                
                # 简单的验证损失
                target = features
                if encoded_features.shape[-1] != target.shape[-1]:
                    target = torch.randn_like(encoded_features)
                
                loss = self.criterion(encoded_features, target)
                total_loss += loss.item()
                num_batches += 1
        
        avg_loss = total_loss / num_batches if num_batches > 0 else 0.0
        return avg_loss
    
    def train(self, num_epochs: int, save_dir: str, save_every: int = 5):
        """完整训练流程"""
        os.makedirs(save_dir, exist_ok=True)
        
        best_val_loss = float('inf')
        
        for epoch in range(num_epochs):
            print(f"\nEpoch {epoch + 1}/{num_epochs}")
            print("-" * 50)
            
            # 训练
            train_loss = self.train_epoch()
            print(f"训练损失: {train_loss:.6f}")
            
            # 验证
            val_loss = self.validate()
            if val_loss > 0:
                print(f"验证损失: {val_loss:.6f}")
            
            # 保存检查点
            if (epoch + 1) % save_every == 0:
                checkpoint_dir = os.path.join(save_dir, f"checkpoint-epoch-{epoch + 1}")
                self.encoder.save_pretrained(checkpoint_dir)
                print(f"✓ 检查点已保存: {checkpoint_dir}")
            
            # 保存最佳模型
            if val_loss > 0 and val_loss < best_val_loss:
                best_val_loss = val_loss
                best_model_dir = os.path.join(save_dir, "best_model")
                self.encoder.save_pretrained(best_model_dir)
                print(f"✓ 最佳模型已保存: {best_model_dir}")


def main():
    parser = argparse.ArgumentParser(description="训练SlideChat编码器")
    parser.add_argument("--data_file", type=str, required=True, help="训练数据JSON文件")
    parser.add_argument("--feature_dir", type=str, required=True, help="WSI特征文件目录")
    parser.add_argument("--save_dir", type=str, default="./checkpoints", help="模型保存目录")
    parser.add_argument("--config", type=str, default="default", choices=["default", "small", "large"], help="模型配置")
    parser.add_argument("--batch_size", type=int, default=2, help="批大小")
    parser.add_argument("--num_epochs", type=int, default=10, help="训练轮数")
    parser.add_argument("--learning_rate", type=float, default=1e-4, help="学习率")
    parser.add_argument("--max_patches", type=int, default=1000, help="最大patch数量")
    parser.add_argument("--freeze_longnet", action="store_true", help="冻结LongNet参数")
    
    args = parser.parse_args()
    
    # 创建编码器
    encoder = create_encoder_from_config(
        args.config,
        freeze_longnet=args.freeze_longnet,
        use_gradient_checkpointing=True
    )
    encoder.print_model_info()
    
    # 创建数据集
    train_dataset = WSIDataset(args.data_file, args.feature_dir, args.max_patches)
    train_dataloader = DataLoader(
        train_dataset, 
        batch_size=args.batch_size, 
        shuffle=True, 
        collate_fn=collate_fn,
        num_workers=4
    )
    
    # 创建训练器
    trainer = SlideChatTrainer(
        encoder=encoder,
        train_dataloader=train_dataloader,
        learning_rate=args.learning_rate
    )
    
    # 开始训练
    trainer.train(args.num_epochs, args.save_dir)
    
    print("✓ 训练完成！")


if __name__ == "__main__":
    main()
